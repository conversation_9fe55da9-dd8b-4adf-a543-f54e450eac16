#!/bin/bash

# Fishivo Otomatik Başlatma Script'i
# Bu script backend, metro ve Android uygulamasını otomatik olarak başlatır

set -e

echo "Fishivo Uygulamasi Baslatiliyor..."

# Renk kodları
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Android SDK kontrolü
check_android_sdk() {
    echo -e "${BLUE}Android SDK kontrol ediliyor...${NC}"
    
    if [ -z "$ANDROID_HOME" ]; then
        export ANDROID_HOME=/Users/<USER>/Library/Android/sdk
        export PATH=$PATH:$ANDROID_HOME/platform-tools:$ANDROID_HOME/tools
        echo -e "${YELLOW}⚠️  ANDROID_HOME ayarlandı: $ANDROID_HOME${NC}"
    fi
    
    if [ ! -d "$ANDROID_HOME" ]; then
        echo -e "${RED}❌ Android SDK bulunamadı: $ANDROID_HOME${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ Android SDK hazır${NC}"
}

# Android cihaz kontrolü
check_android_device() {
    echo -e "${BLUE}📱 Android cihaz kontrol ediliyor...${NC}"
    
    DEVICES=$(adb devices | grep -v "List of devices" | grep "device$" | wc -l)
    
    if [ "$DEVICES" -eq 0 ]; then
        echo -e "${RED}❌ Android cihaz veya emülatör bulunamadı${NC}"
        echo -e "${YELLOW}💡 Lütfen Android cihazınızı bağlayın veya emülatör başlatın${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ Android cihaz hazır ($DEVICES cihaz)${NC}"
}

# Backend başlatma
start_backend() {
    echo -e "${BLUE}🚀 Backend başlatılıyor...${NC}"
    
    # Backend'in zaten çalışıp çalışmadığını kontrol et
    if curl -s http://localhost:3001/health > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Backend zaten çalışıyor${NC}"
        return 0
    fi
    
    # Backend'i arka planda başlat
    cd packages/backend
    yarn ts-node --transpile-only src/index.ts > ../../logs/backend.log 2>&1 &
    BACKEND_PID=$!
    cd ../..
    
    # Backend'in başlamasını bekle
    echo -e "${YELLOW}⏳ Backend başlatılıyor...${NC}"
    for i in {1..30}; do
        if curl -s http://localhost:3001/health > /dev/null 2>&1; then
            echo -e "${GREEN}✅ Backend başarıyla başlatıldı (PID: $BACKEND_PID)${NC}"
            echo $BACKEND_PID > .backend.pid
            return 0
        fi
        sleep 1
        echo -n "."
    done
    
    echo -e "${RED}❌ Backend başlatılamadı${NC}"
    exit 1
}

# Metro başlatma
start_metro() {
    echo -e "${BLUE}📦 Metro bundler başlatılıyor...${NC}"

    # Metro'nun zaten çalışıp çalışmadığını kontrol et (port 8090'ı kontrol et)
    if lsof -ti:8090 > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Metro zaten çalışıyor (Port 8090)${NC}"
        return 0
    fi

    # Metro'yu arka planda başlat
    cd apps/mobile
    yarn react-native start --port 8090 > ../../logs/metro.log 2>&1 &
    METRO_PID=$!
    cd ../..

    # Metro'nun başlamasını bekle (port kontrolü)
    echo -e "${YELLOW}⏳ Metro bundler başlatılıyor...${NC}"
    for i in {1..30}; do
        if lsof -ti:8090 > /dev/null 2>&1; then
            echo -e "${GREEN}✅ Metro bundler başarıyla başlatıldı (PID: $METRO_PID, Port: 8090)${NC}"
            echo $METRO_PID > .metro.pid
            return 0
        fi
        sleep 1
        echo -n "."
    done

    echo -e "${GREEN}✅ Metro bundler başlatıldı (PID: $METRO_PID)${NC}"
    echo $METRO_PID > .metro.pid
}

# Android uygulaması başlatma
start_android_app() {
    echo -e "${BLUE}📱 Android uygulaması başlatılıyor...${NC}"
    
    cd apps/mobile
    
    # Gradle build
    echo -e "${YELLOW}⏳ Android APK build ediliyor...${NC}"
    cd android
    ./gradlew assembleDebug
    
    # APK yükleme
    echo -e "${YELLOW}⏳ APK cihaza yükleniyor...${NC}"
    adb install -r app/build/outputs/apk/debug/app-debug.apk
    
    # Uygulamayı başlat
    echo -e "${YELLOW}⏳ Uygulama başlatılıyor...${NC}"
    adb shell am start -n com.fishivo/.MainActivity
    
    cd ../../..
    
    echo -e "${GREEN}✅ Android uygulaması başarıyla başlatıldı${NC}"
}

# Log klasörü oluştur
mkdir -p logs

# Temizlik fonksiyonu
cleanup() {
    echo -e "\n${YELLOW}🧹 Temizlik yapılıyor...${NC}"
    
    if [ -f .backend.pid ]; then
        BACKEND_PID=$(cat .backend.pid)
        kill $BACKEND_PID 2>/dev/null || true
        rm .backend.pid
        echo -e "${GREEN}✅ Backend durduruldu${NC}"
    fi
    
    if [ -f .metro.pid ]; then
        METRO_PID=$(cat .metro.pid)
        kill $METRO_PID 2>/dev/null || true
        rm .metro.pid
        echo -e "${GREEN}✅ Metro durduruldu${NC}"
    fi
}

# Ctrl+C ile temizlik yap
trap cleanup EXIT

# Ana fonksiyon
main() {
    echo -e "${GREEN}🐟 Fishivo Turborepo Monorepo Başlatılıyor...${NC}"
    
    check_android_sdk
    check_android_device
    start_backend
    start_metro
    start_android_app
    
    echo -e "${GREEN}🎉 Fishivo başarıyla başlatıldı!${NC}"
    echo -e "${BLUE}📊 Servis Durumu:${NC}"
    echo -e "   🔗 Backend: http://localhost:3001"
    echo -e "   📦 Metro: http://localhost:8090"
    echo -e "   📱 Android: Cihazda çalışıyor"
    echo -e "\n${YELLOW}💡 Çıkmak için Ctrl+C tuşlayın${NC}"
    
    # Sonsuz döngü - kullanıcı Ctrl+C yapana kadar bekle
    while true; do
        sleep 1
    done
}

# Script'i çalıştır
main "$@"
